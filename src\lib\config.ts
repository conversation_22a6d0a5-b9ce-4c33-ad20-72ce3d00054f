/**
 * 📌 RistoFlow Configuration
 * 🧠 Central configuration file for the entire application
 */

export const config = {
  // Application
  app: {
    name: 'Risto<PERSON>low',
    description: 'Advanced SaaS platform for restaurants',
    version: '1.0.0',
    url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
    env: process.env.NEXT_PUBLIC_APP_ENV || 'development',
  },

  // Multi-tenant configuration
  multiTenant: {
    mainDomain: process.env.NEXT_PUBLIC_MAIN_DOMAIN || 'ristoflow.com',
    subdomainSuffix: process.env.NEXT_PUBLIC_SUBDOMAIN_SUFFIX || '.ristoflow.com',
    maxSlugLength: 50,
    reservedSlugs: ['www', 'api', 'admin', 'dashboard', 'app', 'mail', 'ftp'],
  },

  // Database
  database: {
    url: process.env.DATABASE_URL,
  },

  // Supabase
  supabase: {
    url: process.env.NEXT_PUBLIC_SUPABASE_URL!,
    anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY,
    storageBucket: process.env.NEXT_PUBLIC_SUPABASE_STORAGE_BUCKET || 'ristoflow-uploads',
  },

  // Authentication
  auth: {
    sessionCookieName: 'ristoflow-session',
    maxAge: 60 * 60 * 24 * 7, // 7 days
    providers: {
      email: true,
      google: true,
      facebook: false,
    },
  },

  // Billing & Subscriptions
  billing: {
    enabled: process.env.NEXT_PUBLIC_ENABLE_BILLING === 'true',
    stripe: {
      publishableKey: process.env.STRIPE_PUBLISHABLE_KEY,
      secretKey: process.env.STRIPE_SECRET_KEY,
      webhookSecret: process.env.STRIPE_WEBHOOK_SECRET,
    },
    plans: {
      free: {
        name: 'Free',
        price: 0,
        features: ['1 Restaurant', 'Basic Templates', 'Community Support'],
        limits: {
          restaurants: 1,
          menuItems: 50,
          orders: 100,
          storage: '100MB',
        },
      },
      pro: {
        name: 'Pro',
        price: 29,
        features: ['5 Restaurants', 'Premium Templates', 'Priority Support', 'Analytics'],
        limits: {
          restaurants: 5,
          menuItems: 500,
          orders: 1000,
          storage: '1GB',
        },
      },
      enterprise: {
        name: 'Enterprise',
        price: 99,
        features: ['Unlimited Restaurants', 'White-label', 'Custom Templates', 'API Access'],
        limits: {
          restaurants: -1, // unlimited
          menuItems: -1,
          orders: -1,
          storage: '10GB',
        },
      },
    },
  },

  // Features
  features: {
    analytics: process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === 'true',
    aiFeatures: process.env.NEXT_PUBLIC_ENABLE_AI_FEATURES === 'true',
    realtime: true,
    fileUpload: true,
    emailNotifications: true,
  },

  // File Upload
  upload: {
    maxFileSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
    maxFiles: 10,
  },

  // Email
  email: {
    from: process.env.SMTP_USER || '<EMAIL>',
    smtp: {
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  },

  // Analytics
  analytics: {
    googleAnalyticsId: process.env.GOOGLE_ANALYTICS_ID,
  },

  // API
  api: {
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // limit each IP to 100 requests per windowMs
    },
  },
} as const

// Type exports
export type Config = typeof config
export type BillingPlan = keyof typeof config.billing.plans
