{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Ristoflow/ristoflow/src/app/page.tsx"], "sourcesContent": ["/**\n * 📌 RistoFlow Landing Page - 2025 Edition\n * 🧠 Ultra-modern landing page with cutting-edge 2025 design trends\n */\n\nimport Link from \"next/link\"\nimport { ArrowRight, CheckCircle, Globe, Smartphone, TrendingUp, Users, Zap, Star, Sparkles, ChefHat, CreditCard, BarChart3, Calendar, MessageSquare } from \"lucide-react\"\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800 text-slate-900 dark:text-white overflow-hidden relative\">\n      {/* Professional Background */}\n      <div className=\"fixed inset-0 -z-10\">\n        <div className=\"absolute inset-0 bg-gradient-to-br from-orange-500/3 via-blue-500/2 to-purple-500/3\"></div>\n        <div className=\"absolute top-0 left-1/4 w-96 h-96 bg-orange-500/5 rounded-full blur-3xl animate-pulse opacity-60\"></div>\n        <div className=\"absolute bottom-0 right-1/4 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl animate-pulse delay-1000 opacity-60\"></div>\n\n        {/* Subtle Grid Pattern */}\n        <div className=\"absolute inset-0 bg-[linear-gradient(rgba(0,0,0,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(0,0,0,0.02)_1px,transparent_1px)] dark:bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:60px_60px]\"></div>\n      </div>\n\n      {/* Navigation - Professional Style */}\n      <nav className=\"relative z-50 border-b border-slate-200/50 dark:border-slate-700/50 bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl\">\n        <div className=\"max-w-7xl mx-auto px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-18\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-10 h-10 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300\">\n                <ChefHat className=\"w-5 h-5 text-white\" />\n              </div>\n              <div>\n                <span className=\"text-2xl font-bold tracking-tight text-slate-900 dark:text-white\">\n                  RistoFlow\n                </span>\n                <div className=\"text-xs text-orange-600 dark:text-orange-400 font-medium\">Restaurant SaaS Platform</div>\n              </div>\n            </div>\n\n            <div className=\"hidden lg:flex items-center space-x-8\">\n              <Link href=\"#features\" className=\"text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white transition-colors font-medium\">\n                Features\n              </Link>\n              <Link href=\"#showcase\" className=\"text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white transition-colors font-medium\">\n                Case Studies\n              </Link>\n              <Link href=\"#pricing\" className=\"text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white transition-colors font-medium\">\n                Pricing\n              </Link>\n              <Link href=\"/demo\" className=\"text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white transition-colors font-medium\">\n                Demo\n              </Link>\n              <Link href=\"/dashboard\" className=\"bg-gradient-to-r from-orange-500 to-red-600 text-white px-6 py-2.5 rounded-lg font-semibold hover:from-orange-600 hover:to-red-700 transition-all duration-200 shadow-lg hover:shadow-xl\">\n                Get Started\n              </Link>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Hero Section - Professional Bento Grid */}\n      <section className=\"relative min-h-screen flex items-center justify-center px-6 lg:px-8 py-20\">\n        <div className=\"max-w-7xl mx-auto w-full\">\n          {/* Professional Bento Grid Layout */}\n          <div className=\"grid grid-cols-12 grid-rows-8 gap-6 h-[85vh] max-h-[700px]\">\n\n            {/* Main Hero Content - Large Professional Box */}\n            <div className=\"col-span-12 lg:col-span-8 row-span-5 bg-white/90 dark:bg-slate-800/90 backdrop-blur-xl rounded-2xl border border-slate-200/50 dark:border-slate-700/50 p-8 lg:p-12 flex flex-col justify-center relative overflow-hidden group hover:shadow-2xl transition-all duration-500 shadow-xl\">\n              <div className=\"absolute inset-0 bg-gradient-to-br from-orange-500/5 to-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500\"></div>\n\n              <div className=\"relative z-10\">\n                <div className=\"inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-orange-100 to-red-100 dark:from-orange-900/30 dark:to-red-900/30 border border-orange-200 dark:border-orange-700 text-orange-700 dark:text-orange-300 text-sm font-semibold mb-8\">\n                  <TrendingUp className=\"w-4 h-4 mr-2\" />\n                  #1 Restaurant SaaS Platform\n                </div>\n\n                <h1 className=\"text-4xl lg:text-6xl xl:text-7xl font-bold leading-tight mb-6 tracking-tight text-slate-900 dark:text-white\">\n                  <span className=\"block\">Transform Your</span>\n                  <span className=\"block bg-gradient-to-r from-orange-600 via-red-600 to-purple-600 bg-clip-text text-transparent\">\n                    Restaurant Business\n                  </span>\n                </h1>\n\n                <p className=\"text-xl lg:text-2xl text-slate-600 dark:text-slate-300 mb-8 max-w-2xl leading-relaxed\">\n                  The complete SaaS solution for modern restaurants. Build professional websites,\n                  streamline operations, and grow revenue with data-driven insights.\n                </p>\n\n                <div className=\"flex flex-col sm:flex-row gap-4 mb-8\">\n                  <Link href=\"/dashboard\" className=\"group bg-gradient-to-r from-orange-500 to-red-600 text-white px-8 py-4 rounded-xl font-semibold hover:from-orange-600 hover:to-red-700 transition-all duration-200 shadow-lg hover:shadow-xl flex items-center justify-center space-x-2\">\n                    <span>Start Free Trial</span>\n                    <ArrowRight className=\"w-5 h-5 group-hover:translate-x-1 transition-transform\" />\n                  </Link>\n\n                  <Link href=\"#demo\" className=\"px-8 py-4 rounded-xl border-2 border-slate-300 dark:border-slate-600 text-slate-700 dark:text-slate-300 hover:border-orange-500 dark:hover:border-orange-500 hover:text-orange-600 dark:hover:text-orange-400 transition-all duration-200 font-semibold flex items-center justify-center space-x-2\">\n                    <Globe className=\"w-5 h-5\" />\n                    <span>Watch Demo</span>\n                  </Link>\n                </div>\n\n                <div className=\"flex items-center space-x-6 text-sm text-slate-500 dark:text-slate-400\">\n                  <div className=\"flex items-center space-x-2\">\n                    <CheckCircle className=\"w-4 h-4 text-green-500\" />\n                    <span>No Credit Card Required</span>\n                  </div>\n                  <div className=\"flex items-center space-x-2\">\n                    <CheckCircle className=\"w-4 h-4 text-green-500\" />\n                    <span>Setup in 5 Minutes</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Stats Card - Professional */}\n            <div className=\"col-span-12 lg:col-span-4 row-span-2 bg-white/90 dark:bg-slate-800/90 backdrop-blur-xl rounded-2xl border border-slate-200/50 dark:border-slate-700/50 p-6 flex flex-col justify-center hover:shadow-xl transition-all duration-500 shadow-lg group\">\n              <div className=\"text-center\">\n                <div className=\"text-4xl font-bold text-green-600 dark:text-green-400 mb-2\">10,000+</div>\n                <div className=\"text-slate-600 dark:text-slate-300 font-medium mb-4\">Restaurants Served</div>\n                <div className=\"flex justify-center items-center space-x-1\">\n                  {[...Array(5)].map((_, i) => (\n                    <Star key={i} className=\"w-4 h-4 text-yellow-400 fill-current\" />\n                  ))}\n                  <span className=\"ml-2 text-sm text-slate-500 dark:text-slate-400\">4.9/5 Rating</span>\n                </div>\n              </div>\n            </div>\n\n            {/* Feature Preview - Professional */}\n            <div className=\"col-span-12 lg:col-span-4 row-span-3 bg-white/90 dark:bg-slate-800/90 backdrop-blur-xl rounded-2xl border border-slate-200/50 dark:border-slate-700/50 p-6 flex flex-col justify-between hover:shadow-xl transition-all duration-500 shadow-lg group\">\n              <div>\n                <div className=\"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-105 transition-transform duration-300 shadow-lg\">\n                  <BarChart3 className=\"w-6 h-6 text-white\" />\n                </div>\n                <h3 className=\"text-xl font-bold text-slate-900 dark:text-white mb-2\">Smart Analytics</h3>\n                <p className=\"text-slate-600 dark:text-slate-300 text-sm leading-relaxed\">Real-time insights and predictive analytics to optimize your restaurant's performance and profitability.</p>\n              </div>\n              <div className=\"mt-6 space-y-3\">\n                <div className=\"flex justify-between text-sm\">\n                  <span className=\"text-slate-500 dark:text-slate-400\">Revenue Growth</span>\n                  <span className=\"text-green-600 dark:text-green-400 font-semibold\">+127%</span>\n                </div>\n                <div className=\"w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2\">\n                  <div className=\"bg-gradient-to-r from-green-500 to-emerald-500 h-2 rounded-full w-4/5\"></div>\n                </div>\n                <div className=\"text-xs text-slate-500 dark:text-slate-400\">Average client improvement</div>\n              </div>\n            </div>\n\n            {/* Trust Indicators - Professional */}\n            <div className=\"col-span-12 lg:col-span-8 row-span-1 bg-white/90 dark:bg-slate-800/90 backdrop-blur-xl rounded-2xl border border-slate-200/50 dark:border-slate-700/50 p-4 flex items-center justify-center space-x-8 hover:shadow-xl transition-all duration-500 shadow-lg\">\n              <div className=\"flex items-center space-x-2 text-slate-600 dark:text-slate-300\">\n                <CheckCircle className=\"w-5 h-5 text-green-500\" />\n                <span className=\"font-medium\">Free 14-Day Trial</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-slate-600 dark:text-slate-300\">\n                <CheckCircle className=\"w-5 h-5 text-green-500\" />\n                <span className=\"font-medium\">Quick Setup</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-slate-600 dark:text-slate-300\">\n                <CheckCircle className=\"w-5 h-5 text-green-500\" />\n                <span className=\"font-medium\">24/7 Support</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section - Professional Design */}\n      <section id=\"features\" className=\"py-24 px-6 lg:px-8 bg-slate-50 dark:bg-slate-900 relative\">\n        <div className=\"max-w-7xl mx-auto\">\n          {/* Section Header */}\n          <div className=\"text-center mb-16\">\n            <div className=\"inline-block bg-gradient-to-r from-orange-500 to-red-600 text-white px-6 py-2 rounded-lg font-semibold text-sm mb-6\">\n              COMPREHENSIVE SOLUTION\n            </div>\n            <h2 className=\"text-4xl lg:text-5xl font-bold text-slate-900 dark:text-white mb-6 leading-tight\">\n              Everything Your Restaurant\n              <span className=\"block bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent\">\n                Needs to Succeed\n              </span>\n            </h2>\n            <p className=\"text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto leading-relaxed\">\n              From professional websites to advanced analytics, RistoFlow provides all the tools modern restaurants need to thrive in the digital economy.\n            </p>\n          </div>\n\n          {/* Professional Features Grid */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-12 gap-8\">\n\n            {/* Website Builder - Large Feature */}\n            <div className=\"lg:col-span-8 bg-white dark:bg-slate-800 rounded-2xl border border-slate-200 dark:border-slate-700 p-8 lg:p-12 group hover:shadow-2xl transition-all duration-500 shadow-xl relative overflow-hidden\">\n              <div className=\"absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-full blur-2xl group-hover:scale-125 transition-transform duration-700\"></div>\n\n              <div className=\"relative z-10\">\n                <div className=\"flex items-start justify-between mb-6\">\n                  <div className=\"w-14 h-14 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center group-hover:scale-105 transition-all duration-300 shadow-lg\">\n                    <Globe className=\"w-7 h-7 text-white\" />\n                  </div>\n                  <div className=\"bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-3 py-1 rounded-full text-xs font-semibold\">\n                    MOST POPULAR\n                  </div>\n                </div>\n\n                <h3 className=\"text-3xl lg:text-4xl font-bold text-slate-900 dark:text-white mb-4\">\n                  Professional Website Builder\n                </h3>\n                <p className=\"text-slate-600 dark:text-slate-300 text-lg mb-6 leading-relaxed\">\n                  Create stunning, conversion-optimized restaurant websites with our intuitive drag-and-drop builder.\n                  No technical skills required.\n                </p>\n\n                <div className=\"grid grid-cols-2 gap-4 mb-8\">\n                  <div className=\"bg-slate-50 dark:bg-slate-700/50 rounded-xl p-4 border border-slate-200 dark:border-slate-600\">\n                    <CheckCircle className=\"w-6 h-6 text-green-500 mb-2\" />\n                    <div className=\"text-slate-900 dark:text-white font-semibold\">50+ Templates</div>\n                    <div className=\"text-slate-600 dark:text-slate-400 text-sm\">Industry-specific designs</div>\n                  </div>\n                  <div className=\"bg-slate-50 dark:bg-slate-700/50 rounded-xl p-4 border border-slate-200 dark:border-slate-600\">\n                    <CheckCircle className=\"w-6 h-6 text-green-500 mb-2\" />\n                    <div className=\"text-slate-900 dark:text-white font-semibold\">SEO Optimized</div>\n                    <div className=\"text-slate-600 dark:text-slate-400 text-sm\">Built-in optimization</div>\n                  </div>\n                </div>\n\n                <Link href=\"/builder\" className=\"inline-flex items-center bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-xl font-semibold hover:from-blue-600 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl\">\n                  Try Builder Free\n                  <ArrowRight className=\"ml-2 w-5 h-5\" />\n                </Link>\n              </div>\n            </div>\n\n            {/* Digital Menu - Vertical Feature */}\n            <div className=\"lg:col-span-4 bg-gradient-to-br from-green-500/10 to-emerald-500/10 backdrop-blur-xl rounded-3xl border border-green-500/20 p-8 group hover:from-green-500/15 hover:to-emerald-500/15 transition-all duration-500 relative overflow-hidden\">\n              <div className=\"absolute -top-10 -right-10 w-24 h-24 bg-gradient-to-br from-green-500/30 to-emerald-500/30 rounded-full blur-xl group-hover:scale-125 transition-transform duration-700\"></div>\n\n              <div className=\"relative z-10\">\n                <div className=\"w-14 h-14 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 group-hover:-rotate-12 transition-all duration-300 shadow-2xl shadow-green-500/25\">\n                  <Smartphone className=\"w-7 h-7 text-white\" />\n                </div>\n\n                <h3 className=\"text-2xl font-black text-white mb-4\">\n                  Smart Digital Menu\n                </h3>\n                <p className=\"text-white/70 mb-6 leading-relaxed\">\n                  QR code ordering, real-time kitchen integration, and AI-powered menu optimization.\n                </p>\n\n                <div className=\"space-y-3 mb-6\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-2 h-2 bg-green-400 rounded-full\"></div>\n                    <span className=\"text-white/80 text-sm\">QR Code Ordering</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-2 h-2 bg-green-400 rounded-full\"></div>\n                    <span className=\"text-white/80 text-sm\">Kitchen Display</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-2 h-2 bg-green-400 rounded-full\"></div>\n                    <span className=\"text-white/80 text-sm\">Payment Integration</span>\n                  </div>\n                </div>\n\n                <div className=\"bg-green-500/20 rounded-2xl p-4 border border-green-500/30\">\n                  <div className=\"text-green-400 font-bold text-lg\">+47%</div>\n                  <div className=\"text-white/70 text-sm\">Average Order Increase</div>\n                </div>\n              </div>\n            </div>\n\n            {/* Analytics - Wide Feature */}\n            <div className=\"lg:col-span-7 bg-gradient-to-br from-purple-500/10 to-pink-500/10 backdrop-blur-xl rounded-3xl border border-purple-500/20 p-8 group hover:from-purple-500/15 hover:to-pink-500/15 transition-all duration-500 relative overflow-hidden\">\n              <div className=\"absolute bottom-0 left-0 w-40 h-40 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-full blur-2xl group-hover:scale-125 transition-transform duration-700\"></div>\n\n              <div className=\"relative z-10\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <div className=\"w-14 h-14 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center group-hover:scale-110 group-hover:rotate-12 transition-all duration-300 shadow-2xl shadow-purple-500/25\">\n                    <TrendingUp className=\"w-7 h-7 text-white\" />\n                  </div>\n                  <div className=\"bg-purple-400 text-black px-3 py-1 rounded-full text-xs font-black\">\n                    AI POWERED\n                  </div>\n                </div>\n\n                <h3 className=\"text-3xl font-black text-white mb-4\">\n                  Predictive Analytics\n                </h3>\n                <p className=\"text-white/70 text-lg mb-6 leading-relaxed\">\n                  AI-driven insights that predict customer behavior, optimize pricing, and maximize your restaurant's profitability.\n                </p>\n\n                <div className=\"grid grid-cols-3 gap-4\">\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-black text-purple-400\">127%</div>\n                    <div className=\"text-white/60 text-sm\">Revenue Growth</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-black text-pink-400\">89%</div>\n                    <div className=\"text-white/60 text-sm\">Cost Reduction</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-black text-purple-400\">24/7</div>\n                    <div className=\"text-white/60 text-sm\">Monitoring</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* CRM - Square Feature */}\n            <div className=\"lg:col-span-5 bg-gradient-to-br from-orange-500/10 to-red-500/10 backdrop-blur-xl rounded-3xl border border-orange-500/20 p-8 group hover:from-orange-500/15 hover:to-red-500/15 transition-all duration-500 relative overflow-hidden\">\n              <div className=\"absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-orange-500/20 to-red-500/20 rounded-full blur-xl group-hover:scale-150 transition-transform duration-700\"></div>\n\n              <div className=\"relative z-10\">\n                <div className=\"w-14 h-14 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 group-hover:-rotate-12 transition-all duration-300 shadow-2xl shadow-orange-500/25\">\n                  <Users className=\"w-7 h-7 text-white\" />\n                </div>\n\n                <h3 className=\"text-2xl font-black text-white mb-4\">\n                  Customer CRM\n                </h3>\n                <p className=\"text-white/70 mb-6 leading-relaxed\">\n                  Build lasting relationships with integrated CRM, loyalty programs, and automated marketing campaigns.\n                </p>\n\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-center justify-between bg-white/5 rounded-xl p-3 border border-white/10\">\n                    <span className=\"text-white/80 text-sm\">Customer Retention</span>\n                    <span className=\"text-orange-400 font-bold\">+156%</span>\n                  </div>\n                  <div className=\"flex items-center justify-between bg-white/5 rounded-xl p-3 border border-white/10\">\n                    <span className=\"text-white/80 text-sm\">Loyalty Members</span>\n                    <span className=\"text-red-400 font-bold\">12.5K</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Showcase Section */}\n      <section id=\"showcase\" className=\"py-32 px-6 lg:px-8 relative\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"text-center mb-20\">\n            <h2 className=\"text-5xl lg:text-6xl font-black text-white mb-6\">\n              See RistoFlow\n              <span className=\"block bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent\">\n                In Action\n              </span>\n            </h2>\n            <p className=\"text-xl text-white/70 max-w-2xl mx-auto\">\n              Real restaurants, real results. See how RistoFlow transforms businesses.\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n            {/* Showcase Item 1 */}\n            <div className=\"group relative bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl rounded-3xl border border-white/20 p-8 hover:from-white/15 hover:to-white/10 transition-all duration-500 overflow-hidden\">\n              <div className=\"absolute inset-0 bg-gradient-to-br from-green-500/5 to-emerald-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500\"></div>\n              <div className=\"relative z-10\">\n                <div className=\"w-full h-48 bg-gradient-to-br from-green-500/20 to-emerald-500/20 rounded-2xl mb-6 flex items-center justify-center border border-green-500/30\">\n                  <div className=\"text-6xl\">🍕</div>\n                </div>\n                <h3 className=\"text-2xl font-black text-white mb-3\">Mario's Pizzeria</h3>\n                <p className=\"text-white/70 mb-4\">Increased online orders by 340% in just 3 months</p>\n                <div className=\"flex items-center space-x-2 text-green-400 font-bold\">\n                  <TrendingUp className=\"w-5 h-5\" />\n                  <span>+340% Orders</span>\n                </div>\n              </div>\n            </div>\n\n            {/* Showcase Item 2 */}\n            <div className=\"group relative bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl rounded-3xl border border-white/20 p-8 hover:from-white/15 hover:to-white/10 transition-all duration-500 overflow-hidden\">\n              <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500\"></div>\n              <div className=\"relative z-10\">\n                <div className=\"w-full h-48 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-2xl mb-6 flex items-center justify-center border border-blue-500/30\">\n                  <div className=\"text-6xl\">🥘</div>\n                </div>\n                <h3 className=\"text-2xl font-black text-white mb-3\">Spice Garden</h3>\n                <p className=\"text-white/70 mb-4\">Streamlined operations and reduced costs by 45%</p>\n                <div className=\"flex items-center space-x-2 text-blue-400 font-bold\">\n                  <BarChart3 className=\"w-5 h-5\" />\n                  <span>-45% Costs</span>\n                </div>\n              </div>\n            </div>\n\n            {/* Showcase Item 3 */}\n            <div className=\"group relative bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl rounded-3xl border border-white/20 p-8 hover:from-white/15 hover:to-white/10 transition-all duration-500 overflow-hidden\">\n              <div className=\"absolute inset-0 bg-gradient-to-br from-orange-500/5 to-red-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500\"></div>\n              <div className=\"relative z-10\">\n                <div className=\"w-full h-48 bg-gradient-to-br from-orange-500/20 to-red-500/20 rounded-2xl mb-6 flex items-center justify-center border border-orange-500/30\">\n                  <div className=\"text-6xl\">☕</div>\n                </div>\n                <h3 className=\"text-2xl font-black text-white mb-3\">Brew & Beans</h3>\n                <p className=\"text-white/70 mb-4\">Built a loyal customer base of 5,000+ members</p>\n                <div className=\"flex items-center space-x-2 text-orange-400 font-bold\">\n                  <Users className=\"w-5 h-5\" />\n                  <span>5K+ Members</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section - Neubrutalist */}\n      <section className=\"py-32 px-6 lg:px-8 relative\">\n        <div className=\"max-w-5xl mx-auto text-center\">\n          <div className=\"bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl rounded-3xl border border-white/20 p-12 lg:p-16 relative overflow-hidden group\">\n            <div className=\"absolute inset-0 bg-gradient-to-br from-orange-500/10 to-red-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-700\"></div>\n\n            <div className=\"relative z-10\">\n              <div className=\"inline-block bg-gradient-to-r from-orange-500 to-red-600 text-white px-6 py-3 rounded-2xl font-black text-sm mb-8 transform rotate-2 hover:rotate-0 transition-transform duration-300 shadow-2xl shadow-orange-500/25\">\n                LIMITED TIME OFFER\n              </div>\n\n              <h2 className=\"text-5xl lg:text-7xl font-black text-white mb-6 leading-tight\">\n                Ready to\n                <span className=\"block bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent\">\n                  Dominate?\n                </span>\n              </h2>\n\n              <p className=\"text-xl lg:text-2xl text-white/70 mb-10 max-w-3xl mx-auto leading-relaxed\">\n                Join 10,000+ restaurants already using RistoFlow to revolutionize their business.\n                Start your free trial today and see results in 24 hours.\n              </p>\n\n              <div className=\"flex flex-col sm:flex-row gap-6 justify-center items-center\">\n                <Link href=\"/dashboard\" className=\"group relative bg-gradient-to-r from-orange-500 to-red-600 text-white px-10 py-5 rounded-2xl font-black text-xl hover:from-orange-400 hover:to-red-500 transition-all duration-300 shadow-2xl shadow-orange-500/25 hover:shadow-orange-500/40 hover:scale-105 transform flex items-center space-x-3\">\n                  <Zap className=\"w-6 h-6\" />\n                  <span>Start Free Trial</span>\n                  <ArrowRight className=\"w-6 h-6 group-hover:translate-x-1 transition-transform\" />\n                  <div className=\"absolute -inset-1 bg-gradient-to-r from-orange-500 to-red-600 rounded-2xl blur opacity-30 group-hover:opacity-50 transition duration-300 -z-10\"></div>\n                </Link>\n\n                <div className=\"text-center\">\n                  <div className=\"text-white/60 text-sm mb-2\">Trusted by</div>\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"flex -space-x-2\">\n                      {[...Array(5)].map((_, i) => (\n                        <div key={i} className=\"w-8 h-8 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full border-2 border-black flex items-center justify-center text-xs font-bold text-black\">\n                          ★\n                        </div>\n                      ))}\n                    </div>\n                    <span className=\"text-white font-bold\">10,000+ restaurants</span>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"mt-8 flex flex-wrap justify-center gap-8 text-white/60 text-sm\">\n                <div className=\"flex items-center space-x-2\">\n                  <CheckCircle className=\"w-5 h-5 text-green-400\" />\n                  <span>No Credit Card Required</span>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <CheckCircle className=\"w-5 h-5 text-green-400\" />\n                  <span>5-Minute Setup</span>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <CheckCircle className=\"w-5 h-5 text-green-400\" />\n                  <span>Cancel Anytime</span>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <CheckCircle className=\"w-5 h-5 text-green-400\" />\n                  <span>24/7 Support</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer - Minimal */}\n      <footer className=\"py-16 px-6 lg:px-8 border-t border-white/10\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"flex flex-col lg:flex-row justify-between items-center\">\n            <div className=\"flex items-center space-x-3 mb-8 lg:mb-0\">\n              <div className=\"w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center shadow-2xl shadow-orange-500/25\">\n                <ChefHat className=\"w-6 h-6 text-white\" />\n              </div>\n              <div>\n                <span className=\"text-2xl font-black text-white\">RistoFlow</span>\n                <div className=\"text-xs text-orange-400 font-medium\">2025 EDITION</div>\n              </div>\n            </div>\n\n            <div className=\"text-center lg:text-right\">\n              <div className=\"text-white/60 text-sm mb-2\">\n                © 2025 RistoFlow. Built with ❤️ for restaurants worldwide.\n              </div>\n              <div className=\"text-white/40 text-xs\">\n                Powered by Next.js 15 • Supabase • AI • Love\n              </div>\n            </div>\n          </div>\n        </div>\n      </footer>\n    </div>\n  )\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,4MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;kDAErB,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAAmE;;;;;;0DAGnF,8OAAC;gDAAI,WAAU;0DAA2D;;;;;;;;;;;;;;;;;;0CAI9E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAA8G;;;;;;kDAG/I,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAA8G;;;;;;kDAG/I,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAA8G;;;;;;kDAG9I,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAQ,WAAU;kDAA8G;;;;;;kDAG3I,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;kDAA2L;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASrO,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;wBAAI,WAAU;;0CAGb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDAEf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAIzC,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAK,WAAU;kEAAQ;;;;;;kEACxB,8OAAC;wDAAK,WAAU;kEAAiG;;;;;;;;;;;;0DAKnH,8OAAC;gDAAE,WAAU;0DAAwF;;;;;;0DAKrG,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAa,WAAU;;0EAChC,8OAAC;0EAAK;;;;;;0EACN,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;kEAGxB,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAQ,WAAU;;0EAC3B,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;0DAIV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOd,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAA6D;;;;;;sDAC5E,8OAAC;4CAAI,WAAU;sDAAsD;;;;;;sDACrE,8OAAC;4CAAI,WAAU;;gDACZ;uDAAI,MAAM;iDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,kMAAA,CAAA,OAAI;wDAAS,WAAU;uDAAb;;;;;8DAEb,8OAAC;oDAAK,WAAU;8DAAkD;;;;;;;;;;;;;;;;;;;;;;;0CAMxE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;0DAEvB,8OAAC;gDAAG,WAAU;0DAAwD;;;;;;0DACtE,8OAAC;gDAAE,WAAU;0DAA6D;;;;;;;;;;;;kDAE5E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAqC;;;;;;kEACrD,8OAAC;wDAAK,WAAU;kEAAmD;;;;;;;;;;;;0DAErE,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;;;;;;;;;;0DAEjB,8OAAC;gDAAI,WAAU;0DAA6C;;;;;;;;;;;;;;;;;;0CAKhE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,8OAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;kDAEhC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,8OAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;kDAEhC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,8OAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQxC,8OAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAsH;;;;;;8CAGrI,8OAAC;oCAAG,WAAU;;wCAAmF;sDAE/F,8OAAC;4CAAK,WAAU;sDAAkF;;;;;;;;;;;;8CAIpG,8OAAC;oCAAE,WAAU;8CAA+E;;;;;;;;;;;;sCAM9F,8OAAC;4BAAI,WAAU;;8CAGb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDAEf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;sEAEnB,8OAAC;4DAAI,WAAU;sEAAoH;;;;;;;;;;;;8DAKrI,8OAAC;oDAAG,WAAU;8DAAqE;;;;;;8DAGnF,8OAAC;oDAAE,WAAU;8DAAkE;;;;;;8DAK/E,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;8EACvB,8OAAC;oEAAI,WAAU;8EAA+C;;;;;;8EAC9D,8OAAC;oEAAI,WAAU;8EAA6C;;;;;;;;;;;;sEAE9D,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;8EACvB,8OAAC;oEAAI,WAAU;8EAA+C;;;;;;8EAC9D,8OAAC;oEAAI,WAAU;8EAA6C;;;;;;;;;;;;;;;;;;8DAIhE,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;;wDAAoN;sEAElP,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;8CAM5B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDAEf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,8MAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;8DAGxB,8OAAC;oDAAG,WAAU;8DAAsC;;;;;;8DAGpD,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAIlD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;;;;;;;sEAE1C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;;;;;;;sEAE1C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAI5C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAmC;;;;;;sEAClD,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;8CAM7C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDAEf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;sEAExB,8OAAC;4DAAI,WAAU;sEAAqE;;;;;;;;;;;;8DAKtF,8OAAC;oDAAG,WAAU;8DAAsC;;;;;;8DAGpD,8OAAC;oDAAE,WAAU;8DAA6C;;;;;;8DAI1D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAsC;;;;;;8EACrD,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;sEAEzC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAoC;;;;;;8EACnD,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;sEAEzC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAsC;;;;;;8EACrD,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAO/C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDAEf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAGnB,8OAAC;oDAAG,WAAU;8DAAsC;;;;;;8DAGpD,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAIlD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,8OAAC;oEAAK,WAAU;8EAA4B;;;;;;;;;;;;sEAE9C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,8OAAC;oEAAK,WAAU;8EAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUvD,8OAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCAAkD;sDAE9D,8OAAC;4CAAK,WAAU;sDAAkF;;;;;;;;;;;;8CAIpG,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEAAW;;;;;;;;;;;8DAE5B,8OAAC;oDAAG,WAAU;8DAAsC;;;;;;8DACpD,8OAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAClC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;8CAMZ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEAAW;;;;;;;;;;;8DAE5B,8OAAC;oDAAG,WAAU;8DAAsC;;;;;;8DACpD,8OAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAClC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;sEACrB,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;8CAMZ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEAAW;;;;;;;;;;;8DAE5B,8OAAC;oDAAG,WAAU;8DAAsC;;;;;;8DACpD,8OAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAClC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAwN;;;;;;kDAIvO,8OAAC;wCAAG,WAAU;;4CAAgE;0DAE5E,8OAAC;gDAAK,WAAU;0DAAkF;;;;;;;;;;;;kDAKpG,8OAAC;wCAAE,WAAU;kDAA4E;;;;;;kDAKzF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;;kEAChC,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEACf,8OAAC;kEAAK;;;;;;kEACN,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,8OAAC;wDAAI,WAAU;;;;;;;;;;;;0DAGjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAA6B;;;;;;kEAC5C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACZ;uEAAI,MAAM;iEAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;wEAAY,WAAU;kFAA2J;uEAAxK;;;;;;;;;;0EAKd,8OAAC;gEAAK,WAAU;0EAAuB;;;;;;;;;;;;;;;;;;;;;;;;kDAK7C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlB,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,4MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;kDAErB,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAAiC;;;;;;0DACjD,8OAAC;gDAAI,WAAU;0DAAsC;;;;;;;;;;;;;;;;;;0CAIzD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAG5C,8OAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrD", "debugId": null}}]}