{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Ristoflow/ristoflow/src/app/page.tsx"], "sourcesContent": ["/**\n * 📌 RistoFlow Landing Page - 2025 Edition\n * 🧠 Ultra-modern landing page with cutting-edge 2025 design trends\n */\n\nimport Link from \"next/link\"\nimport { ArrowRight, CheckCircle, Globe, Smartphone, TrendingUp, Users, Zap, Star, Sparkles, ChefHat, CreditCard, BarChart3, Calendar, MessageSquare } from \"lucide-react\"\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-[#0a0a0a] text-white overflow-hidden relative\">\n      {/* Animated Background */}\n      <div className=\"fixed inset-0 -z-10\">\n        <div className=\"absolute inset-0 bg-gradient-to-br from-orange-500/5 via-red-500/5 to-pink-500/5\"></div>\n        <div className=\"absolute top-0 left-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl animate-pulse\"></div>\n        <div className=\"absolute bottom-0 right-1/4 w-96 h-96 bg-red-500/10 rounded-full blur-3xl animate-pulse delay-1000\"></div>\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-conic from-orange-500/5 via-red-500/5 to-pink-500/5 rounded-full blur-3xl animate-spin-slow\"></div>\n\n        {/* Grid Pattern */}\n        <div className=\"absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]\"></div>\n      </div>\n\n      {/* Navigation - Neubrutalist Style */}\n      <nav className=\"relative z-50 border-b border-white/10 bg-black/50 backdrop-blur-xl\">\n        <div className=\"max-w-7xl mx-auto px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-20\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"relative\">\n                <div className=\"w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center shadow-2xl shadow-orange-500/25 rotate-3 hover:rotate-0 transition-transform duration-300\">\n                  <ChefHat className=\"w-6 h-6 text-white\" />\n                </div>\n                <div className=\"absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full animate-ping\"></div>\n              </div>\n              <div>\n                <span className=\"text-2xl font-black tracking-tight bg-gradient-to-r from-white via-orange-200 to-red-200 bg-clip-text text-transparent\">\n                  RistoFlow\n                </span>\n                <div className=\"text-xs text-orange-400 font-medium\">2025 EDITION</div>\n              </div>\n            </div>\n\n            <div className=\"hidden lg:flex items-center space-x-8\">\n              <Link href=\"#features\" className=\"text-white/70 hover:text-white transition-colors font-medium hover:scale-105 transform duration-200\">\n                Features\n              </Link>\n              <Link href=\"#showcase\" className=\"text-white/70 hover:text-white transition-colors font-medium hover:scale-105 transform duration-200\">\n                Showcase\n              </Link>\n              <Link href=\"#pricing\" className=\"text-white/70 hover:text-white transition-colors font-medium hover:scale-105 transform duration-200\">\n                Pricing\n              </Link>\n              <div className=\"relative group\">\n                <Link href=\"/dashboard\" className=\"bg-gradient-to-r from-orange-500 to-red-600 text-white px-6 py-3 rounded-2xl font-bold hover:from-orange-400 hover:to-red-500 transition-all duration-300 shadow-2xl shadow-orange-500/25 hover:shadow-orange-500/40 hover:scale-105 transform flex items-center space-x-2 border-2 border-orange-400/20\">\n                  <Sparkles className=\"w-4 h-4\" />\n                  <span>Launch App</span>\n                </Link>\n                <div className=\"absolute -inset-1 bg-gradient-to-r from-orange-500 to-red-600 rounded-2xl blur opacity-30 group-hover:opacity-50 transition duration-300 -z-10\"></div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Hero Section - Spatial Design */}\n      <section className=\"relative min-h-screen flex items-center justify-center px-6 lg:px-8\">\n        <div className=\"max-w-7xl mx-auto w-full\">\n          {/* Bento Grid Layout */}\n          <div className=\"grid grid-cols-12 grid-rows-8 gap-4 h-[90vh] max-h-[800px]\">\n\n            {/* Main Hero Content - Large Bento Box */}\n            <div className=\"col-span-12 lg:col-span-8 row-span-5 bg-gradient-to-br from-white/5 to-white/10 backdrop-blur-xl rounded-3xl border border-white/20 p-8 lg:p-12 flex flex-col justify-center relative overflow-hidden group hover:from-white/10 hover:to-white/15 transition-all duration-500\">\n              <div className=\"absolute inset-0 bg-gradient-to-br from-orange-500/5 to-red-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500\"></div>\n\n              <div className=\"relative z-10\">\n                <div className=\"inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-orange-500/20 to-red-500/20 border border-orange-500/30 text-orange-300 text-sm font-bold mb-8 backdrop-blur-sm\">\n                  <Sparkles className=\"w-4 h-4 mr-2 animate-pulse\" />\n                  REVOLUTIONARY RESTAURANT TECH\n                </div>\n\n                <h1 className=\"text-4xl lg:text-7xl xl:text-8xl font-black leading-none mb-6 tracking-tight\">\n                  <span className=\"block text-white\">Build Your</span>\n                  <span className=\"block bg-gradient-to-r from-orange-400 via-red-400 to-pink-400 bg-clip-text text-transparent animate-gradient-x\">\n                    Digital Empire\n                  </span>\n                </h1>\n\n                <p className=\"text-xl lg:text-2xl text-white/70 mb-8 max-w-2xl leading-relaxed font-medium\">\n                  The most advanced SaaS platform for restaurants. Create stunning websites,\n                  manage operations, and scale your business with AI-powered insights.\n                </p>\n\n                <div className=\"flex flex-col sm:flex-row gap-4\">\n                  <Link href=\"/dashboard\" className=\"group relative bg-gradient-to-r from-orange-500 to-red-600 text-white px-8 py-4 rounded-2xl font-bold hover:from-orange-400 hover:to-red-500 transition-all duration-300 shadow-2xl shadow-orange-500/25 hover:shadow-orange-500/40 hover:scale-105 transform flex items-center justify-center space-x-2\">\n                    <Zap className=\"w-5 h-5\" />\n                    <span>Start Building Free</span>\n                    <ArrowRight className=\"w-5 h-5 group-hover:translate-x-1 transition-transform\" />\n                    <div className=\"absolute -inset-1 bg-gradient-to-r from-orange-500 to-red-600 rounded-2xl blur opacity-30 group-hover:opacity-50 transition duration-300 -z-10\"></div>\n                  </Link>\n\n                  <Link href=\"#showcase\" className=\"px-8 py-4 rounded-2xl border-2 border-white/20 text-white hover:border-orange-500/50 hover:bg-white/5 transition-all duration-300 font-bold flex items-center justify-center space-x-2 backdrop-blur-sm\">\n                    <Globe className=\"w-5 h-5\" />\n                    <span>View Showcase</span>\n                  </Link>\n                </div>\n              </div>\n\n              {/* Floating Elements */}\n              <div className=\"absolute top-4 right-4 w-16 h-16 bg-gradient-to-br from-orange-500/20 to-red-500/20 rounded-2xl backdrop-blur-sm border border-white/10 flex items-center justify-center rotate-12 hover:rotate-0 transition-transform duration-300\">\n                <ChefHat className=\"w-8 h-8 text-orange-300\" />\n              </div>\n            </div>\n\n            {/* Stats Card */}\n            <div className=\"col-span-12 lg:col-span-4 row-span-2 bg-gradient-to-br from-green-500/10 to-emerald-500/10 backdrop-blur-xl rounded-3xl border border-green-500/20 p-6 flex flex-col justify-center hover:from-green-500/15 hover:to-emerald-500/15 transition-all duration-500 group\">\n              <div className=\"text-center\">\n                <div className=\"text-4xl font-black text-green-400 mb-2\">10K+</div>\n                <div className=\"text-white/70 font-medium\">Restaurants Powered</div>\n                <div className=\"mt-4 flex justify-center\">\n                  <div className=\"flex -space-x-2\">\n                    {[...Array(5)].map((_, i) => (\n                      <div key={i} className=\"w-8 h-8 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full border-2 border-black flex items-center justify-center text-xs font-bold text-black\">\n                        {i + 1}\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Feature Preview */}\n            <div className=\"col-span-12 lg:col-span-4 row-span-3 bg-gradient-to-br from-blue-500/10 to-purple-500/10 backdrop-blur-xl rounded-3xl border border-blue-500/20 p-6 flex flex-col justify-between hover:from-blue-500/15 hover:to-purple-500/15 transition-all duration-500 group\">\n              <div>\n                <div className=\"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\">\n                  <BarChart3 className=\"w-6 h-6 text-white\" />\n                </div>\n                <h3 className=\"text-xl font-bold text-white mb-2\">AI Analytics</h3>\n                <p className=\"text-white/70 text-sm\">Smart insights that predict trends and optimize your menu for maximum profit.</p>\n              </div>\n              <div className=\"mt-4 space-y-2\">\n                <div className=\"flex justify-between text-sm\">\n                  <span className=\"text-white/60\">Revenue Growth</span>\n                  <span className=\"text-green-400 font-bold\">+127%</span>\n                </div>\n                <div className=\"w-full bg-white/10 rounded-full h-2\">\n                  <div className=\"bg-gradient-to-r from-green-400 to-emerald-500 h-2 rounded-full w-4/5 animate-pulse\"></div>\n                </div>\n              </div>\n            </div>\n\n            {/* Trust Indicators */}\n            <div className=\"col-span-12 lg:col-span-8 row-span-1 bg-gradient-to-r from-white/5 to-white/10 backdrop-blur-xl rounded-3xl border border-white/20 p-4 flex items-center justify-center space-x-8 hover:from-white/10 hover:to-white/15 transition-all duration-500\">\n              <div className=\"flex items-center space-x-2 text-white/70\">\n                <CheckCircle className=\"w-5 h-5 text-green-400\" />\n                <span className=\"font-medium\">No Credit Card Required</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-white/70\">\n                <CheckCircle className=\"w-5 h-5 text-green-400\" />\n                <span className=\"font-medium\">5-Minute Setup</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-white/70\">\n                <CheckCircle className=\"w-5 h-5 text-green-400\" />\n                <span className=\"font-medium\">Cancel Anytime</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section - Neubrutalist Design */}\n      <section id=\"features\" className=\"py-32 px-6 lg:px-8 relative\">\n        <div className=\"max-w-7xl mx-auto\">\n          {/* Section Header */}\n          <div className=\"text-center mb-20\">\n            <div className=\"inline-block bg-gradient-to-r from-orange-500 to-red-600 text-white px-6 py-3 rounded-2xl font-black text-sm mb-8 transform -rotate-2 hover:rotate-0 transition-transform duration-300 shadow-2xl shadow-orange-500/25\">\n              POWERFUL FEATURES\n            </div>\n            <h2 className=\"text-5xl lg:text-7xl font-black text-white mb-6 leading-tight\">\n              Everything Your\n              <span className=\"block bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent\">\n                Restaurant Needs\n              </span>\n            </h2>\n            <p className=\"text-xl text-white/70 max-w-3xl mx-auto leading-relaxed\">\n              From stunning websites to AI-powered operations management, RistoFlow revolutionizes how restaurants operate in the digital age.\n            </p>\n          </div>\n\n          {/* Asymmetric Features Grid */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-12 gap-6\">\n\n            {/* Website Builder - Large Feature */}\n            <div className=\"lg:col-span-8 bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl rounded-3xl border border-white/20 p-8 lg:p-12 group hover:from-white/15 hover:to-white/10 transition-all duration-500 relative overflow-hidden\">\n              <div className=\"absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full blur-2xl group-hover:scale-150 transition-transform duration-700\"></div>\n\n              <div className=\"relative z-10\">\n                <div className=\"flex items-start justify-between mb-6\">\n                  <div className=\"w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center group-hover:scale-110 group-hover:rotate-12 transition-all duration-300 shadow-2xl shadow-blue-500/25\">\n                    <Globe className=\"w-8 h-8 text-white\" />\n                  </div>\n                  <div className=\"bg-green-400 text-black px-3 py-1 rounded-full text-xs font-black\">\n                    MOST POPULAR\n                  </div>\n                </div>\n\n                <h3 className=\"text-3xl lg:text-4xl font-black text-white mb-4\">\n                  Visual Website Builder\n                </h3>\n                <p className=\"text-white/70 text-lg mb-6 leading-relaxed\">\n                  Create stunning restaurant websites with our drag-and-drop builder.\n                  No coding required, just pure creativity unleashed.\n                </p>\n\n                <div className=\"grid grid-cols-2 gap-4 mb-6\">\n                  <div className=\"bg-white/5 rounded-2xl p-4 border border-white/10\">\n                    <CheckCircle className=\"w-6 h-6 text-green-400 mb-2\" />\n                    <div className=\"text-white font-bold\">50+ Templates</div>\n                    <div className=\"text-white/60 text-sm\">Professional designs</div>\n                  </div>\n                  <div className=\"bg-white/5 rounded-2xl p-4 border border-white/10\">\n                    <CheckCircle className=\"w-6 h-6 text-green-400 mb-2\" />\n                    <div className=\"text-white font-bold\">Mobile First</div>\n                    <div className=\"text-white/60 text-sm\">Responsive design</div>\n                  </div>\n                </div>\n\n                <Link href=\"/builder\" className=\"inline-flex items-center bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-2xl font-bold hover:from-blue-400 hover:to-purple-500 transition-all duration-300 shadow-xl shadow-blue-500/25 hover:shadow-blue-500/40 hover:scale-105 transform\">\n                  Try Builder\n                  <ArrowRight className=\"ml-2 w-5 h-5\" />\n                </Link>\n              </div>\n            </div>\n\n            {/* Digital Menu - Vertical Feature */}\n            <div className=\"lg:col-span-4 bg-gradient-to-br from-green-500/10 to-emerald-500/10 backdrop-blur-xl rounded-3xl border border-green-500/20 p-8 group hover:from-green-500/15 hover:to-emerald-500/15 transition-all duration-500 relative overflow-hidden\">\n              <div className=\"absolute -top-10 -right-10 w-24 h-24 bg-gradient-to-br from-green-500/30 to-emerald-500/30 rounded-full blur-xl group-hover:scale-125 transition-transform duration-700\"></div>\n\n              <div className=\"relative z-10\">\n                <div className=\"w-14 h-14 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 group-hover:-rotate-12 transition-all duration-300 shadow-2xl shadow-green-500/25\">\n                  <Smartphone className=\"w-7 h-7 text-white\" />\n                </div>\n\n                <h3 className=\"text-2xl font-black text-white mb-4\">\n                  Smart Digital Menu\n                </h3>\n                <p className=\"text-white/70 mb-6 leading-relaxed\">\n                  QR code ordering, real-time kitchen integration, and AI-powered menu optimization.\n                </p>\n\n                <div className=\"space-y-3 mb-6\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-2 h-2 bg-green-400 rounded-full\"></div>\n                    <span className=\"text-white/80 text-sm\">QR Code Ordering</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-2 h-2 bg-green-400 rounded-full\"></div>\n                    <span className=\"text-white/80 text-sm\">Kitchen Display</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-2 h-2 bg-green-400 rounded-full\"></div>\n                    <span className=\"text-white/80 text-sm\">Payment Integration</span>\n                  </div>\n                </div>\n\n                <div className=\"bg-green-500/20 rounded-2xl p-4 border border-green-500/30\">\n                  <div className=\"text-green-400 font-bold text-lg\">+47%</div>\n                  <div className=\"text-white/70 text-sm\">Average Order Increase</div>\n                </div>\n              </div>\n            </div>\n\n            {/* Analytics - Wide Feature */}\n            <div className=\"lg:col-span-7 bg-gradient-to-br from-purple-500/10 to-pink-500/10 backdrop-blur-xl rounded-3xl border border-purple-500/20 p-8 group hover:from-purple-500/15 hover:to-pink-500/15 transition-all duration-500 relative overflow-hidden\">\n              <div className=\"absolute bottom-0 left-0 w-40 h-40 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-full blur-2xl group-hover:scale-125 transition-transform duration-700\"></div>\n\n              <div className=\"relative z-10\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <div className=\"w-14 h-14 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center group-hover:scale-110 group-hover:rotate-12 transition-all duration-300 shadow-2xl shadow-purple-500/25\">\n                    <TrendingUp className=\"w-7 h-7 text-white\" />\n                  </div>\n                  <div className=\"bg-purple-400 text-black px-3 py-1 rounded-full text-xs font-black\">\n                    AI POWERED\n                  </div>\n                </div>\n\n                <h3 className=\"text-3xl font-black text-white mb-4\">\n                  Predictive Analytics\n                </h3>\n                <p className=\"text-white/70 text-lg mb-6 leading-relaxed\">\n                  AI-driven insights that predict customer behavior, optimize pricing, and maximize your restaurant's profitability.\n                </p>\n\n                <div className=\"grid grid-cols-3 gap-4\">\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-black text-purple-400\">127%</div>\n                    <div className=\"text-white/60 text-sm\">Revenue Growth</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-black text-pink-400\">89%</div>\n                    <div className=\"text-white/60 text-sm\">Cost Reduction</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-black text-purple-400\">24/7</div>\n                    <div className=\"text-white/60 text-sm\">Monitoring</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* CRM - Square Feature */}\n            <div className=\"lg:col-span-5 bg-gradient-to-br from-orange-500/10 to-red-500/10 backdrop-blur-xl rounded-3xl border border-orange-500/20 p-8 group hover:from-orange-500/15 hover:to-red-500/15 transition-all duration-500 relative overflow-hidden\">\n              <div className=\"absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-orange-500/20 to-red-500/20 rounded-full blur-xl group-hover:scale-150 transition-transform duration-700\"></div>\n\n              <div className=\"relative z-10\">\n                <div className=\"w-14 h-14 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 group-hover:-rotate-12 transition-all duration-300 shadow-2xl shadow-orange-500/25\">\n                  <Users className=\"w-7 h-7 text-white\" />\n                </div>\n\n                <h3 className=\"text-2xl font-black text-white mb-4\">\n                  Customer CRM\n                </h3>\n                <p className=\"text-white/70 mb-6 leading-relaxed\">\n                  Build lasting relationships with integrated CRM, loyalty programs, and automated marketing campaigns.\n                </p>\n\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-center justify-between bg-white/5 rounded-xl p-3 border border-white/10\">\n                    <span className=\"text-white/80 text-sm\">Customer Retention</span>\n                    <span className=\"text-orange-400 font-bold\">+156%</span>\n                  </div>\n                  <div className=\"flex items-center justify-between bg-white/5 rounded-xl p-3 border border-white/10\">\n                    <span className=\"text-white/80 text-sm\">Loyalty Members</span>\n                    <span className=\"text-red-400 font-bold\">12.5K</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Showcase Section */}\n      <section id=\"showcase\" className=\"py-32 px-6 lg:px-8 relative\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"text-center mb-20\">\n            <h2 className=\"text-5xl lg:text-6xl font-black text-white mb-6\">\n              See RistoFlow\n              <span className=\"block bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent\">\n                In Action\n              </span>\n            </h2>\n            <p className=\"text-xl text-white/70 max-w-2xl mx-auto\">\n              Real restaurants, real results. See how RistoFlow transforms businesses.\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n            {/* Showcase Item 1 */}\n            <div className=\"group relative bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl rounded-3xl border border-white/20 p-8 hover:from-white/15 hover:to-white/10 transition-all duration-500 overflow-hidden\">\n              <div className=\"absolute inset-0 bg-gradient-to-br from-green-500/5 to-emerald-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500\"></div>\n              <div className=\"relative z-10\">\n                <div className=\"w-full h-48 bg-gradient-to-br from-green-500/20 to-emerald-500/20 rounded-2xl mb-6 flex items-center justify-center border border-green-500/30\">\n                  <div className=\"text-6xl\">🍕</div>\n                </div>\n                <h3 className=\"text-2xl font-black text-white mb-3\">Mario's Pizzeria</h3>\n                <p className=\"text-white/70 mb-4\">Increased online orders by 340% in just 3 months</p>\n                <div className=\"flex items-center space-x-2 text-green-400 font-bold\">\n                  <TrendingUp className=\"w-5 h-5\" />\n                  <span>+340% Orders</span>\n                </div>\n              </div>\n            </div>\n\n            {/* Showcase Item 2 */}\n            <div className=\"group relative bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl rounded-3xl border border-white/20 p-8 hover:from-white/15 hover:to-white/10 transition-all duration-500 overflow-hidden\">\n              <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500\"></div>\n              <div className=\"relative z-10\">\n                <div className=\"w-full h-48 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-2xl mb-6 flex items-center justify-center border border-blue-500/30\">\n                  <div className=\"text-6xl\">🥘</div>\n                </div>\n                <h3 className=\"text-2xl font-black text-white mb-3\">Spice Garden</h3>\n                <p className=\"text-white/70 mb-4\">Streamlined operations and reduced costs by 45%</p>\n                <div className=\"flex items-center space-x-2 text-blue-400 font-bold\">\n                  <BarChart3 className=\"w-5 h-5\" />\n                  <span>-45% Costs</span>\n                </div>\n              </div>\n            </div>\n\n            {/* Showcase Item 3 */}\n            <div className=\"group relative bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl rounded-3xl border border-white/20 p-8 hover:from-white/15 hover:to-white/10 transition-all duration-500 overflow-hidden\">\n              <div className=\"absolute inset-0 bg-gradient-to-br from-orange-500/5 to-red-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500\"></div>\n              <div className=\"relative z-10\">\n                <div className=\"w-full h-48 bg-gradient-to-br from-orange-500/20 to-red-500/20 rounded-2xl mb-6 flex items-center justify-center border border-orange-500/30\">\n                  <div className=\"text-6xl\">☕</div>\n                </div>\n                <h3 className=\"text-2xl font-black text-white mb-3\">Brew & Beans</h3>\n                <p className=\"text-white/70 mb-4\">Built a loyal customer base of 5,000+ members</p>\n                <div className=\"flex items-center space-x-2 text-orange-400 font-bold\">\n                  <Users className=\"w-5 h-5\" />\n                  <span>5K+ Members</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section - Neubrutalist */}\n      <section className=\"py-32 px-6 lg:px-8 relative\">\n        <div className=\"max-w-5xl mx-auto text-center\">\n          <div className=\"bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl rounded-3xl border border-white/20 p-12 lg:p-16 relative overflow-hidden group\">\n            <div className=\"absolute inset-0 bg-gradient-to-br from-orange-500/10 to-red-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-700\"></div>\n\n            <div className=\"relative z-10\">\n              <div className=\"inline-block bg-gradient-to-r from-orange-500 to-red-600 text-white px-6 py-3 rounded-2xl font-black text-sm mb-8 transform rotate-2 hover:rotate-0 transition-transform duration-300 shadow-2xl shadow-orange-500/25\">\n                LIMITED TIME OFFER\n              </div>\n\n              <h2 className=\"text-5xl lg:text-7xl font-black text-white mb-6 leading-tight\">\n                Ready to\n                <span className=\"block bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent\">\n                  Dominate?\n                </span>\n              </h2>\n\n              <p className=\"text-xl lg:text-2xl text-white/70 mb-10 max-w-3xl mx-auto leading-relaxed\">\n                Join 10,000+ restaurants already using RistoFlow to revolutionize their business.\n                Start your free trial today and see results in 24 hours.\n              </p>\n\n              <div className=\"flex flex-col sm:flex-row gap-6 justify-center items-center\">\n                <Link href=\"/dashboard\" className=\"group relative bg-gradient-to-r from-orange-500 to-red-600 text-white px-10 py-5 rounded-2xl font-black text-xl hover:from-orange-400 hover:to-red-500 transition-all duration-300 shadow-2xl shadow-orange-500/25 hover:shadow-orange-500/40 hover:scale-105 transform flex items-center space-x-3\">\n                  <Zap className=\"w-6 h-6\" />\n                  <span>Start Free Trial</span>\n                  <ArrowRight className=\"w-6 h-6 group-hover:translate-x-1 transition-transform\" />\n                  <div className=\"absolute -inset-1 bg-gradient-to-r from-orange-500 to-red-600 rounded-2xl blur opacity-30 group-hover:opacity-50 transition duration-300 -z-10\"></div>\n                </Link>\n\n                <div className=\"text-center\">\n                  <div className=\"text-white/60 text-sm mb-2\">Trusted by</div>\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"flex -space-x-2\">\n                      {[...Array(5)].map((_, i) => (\n                        <div key={i} className=\"w-8 h-8 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full border-2 border-black flex items-center justify-center text-xs font-bold text-black\">\n                          ★\n                        </div>\n                      ))}\n                    </div>\n                    <span className=\"text-white font-bold\">10,000+ restaurants</span>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"mt-8 flex flex-wrap justify-center gap-8 text-white/60 text-sm\">\n                <div className=\"flex items-center space-x-2\">\n                  <CheckCircle className=\"w-5 h-5 text-green-400\" />\n                  <span>No Credit Card Required</span>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <CheckCircle className=\"w-5 h-5 text-green-400\" />\n                  <span>5-Minute Setup</span>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <CheckCircle className=\"w-5 h-5 text-green-400\" />\n                  <span>Cancel Anytime</span>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <CheckCircle className=\"w-5 h-5 text-green-400\" />\n                  <span>24/7 Support</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer - Minimal */}\n      <footer className=\"py-16 px-6 lg:px-8 border-t border-white/10\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"flex flex-col lg:flex-row justify-between items-center\">\n            <div className=\"flex items-center space-x-3 mb-8 lg:mb-0\">\n              <div className=\"w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center shadow-2xl shadow-orange-500/25\">\n                <ChefHat className=\"w-6 h-6 text-white\" />\n              </div>\n              <div>\n                <span className=\"text-2xl font-black text-white\">RistoFlow</span>\n                <div className=\"text-xs text-orange-400 font-medium\">2025 EDITION</div>\n              </div>\n            </div>\n\n            <div className=\"text-center lg:text-right\">\n              <div className=\"text-white/60 text-sm mb-2\">\n                © 2025 RistoFlow. Built with ❤️ for restaurants worldwide.\n              </div>\n              <div className=\"text-white/40 text-xs\">\n                Powered by Next.js 15 • Supabase • AI • Love\n              </div>\n            </div>\n          </div>\n        </div>\n      </footer>\n    </div>\n  )\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,4MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;0DAErB,8OAAC;gDAAI,WAAU;;;;;;;;;;;;kDAEjB,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAAyH;;;;;;0DAGzI,8OAAC;gDAAI,WAAU;0DAAsC;;;;;;;;;;;;;;;;;;0CAIzD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAAsG;;;;;;kDAGvI,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAAsG;;;;;;kDAGvI,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAsG;;;;;;kDAGtI,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;;kEAChC,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQzB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;wBAAI,WAAU;;0CAGb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDAEf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAA+B;;;;;;;0DAIrD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAK,WAAU;kEAAmB;;;;;;kEACnC,8OAAC;wDAAK,WAAU;kEAAkH;;;;;;;;;;;;0DAKpI,8OAAC;gDAAE,WAAU;0DAA+E;;;;;;0DAK5F,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAa,WAAU;;0EAChC,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;0EACf,8OAAC;0EAAK;;;;;;0EACN,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,8OAAC;gEAAI,WAAU;;;;;;;;;;;;kEAGjB,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAY,WAAU;;0EAC/B,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;kDAMZ,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,4MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAKvB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAA0C;;;;;;sDACzD,8OAAC;4CAAI,WAAU;sDAA4B;;;;;;sDAC3C,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACZ;uDAAI,MAAM;iDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;wDAAY,WAAU;kEACpB,IAAI;uDADG;;;;;;;;;;;;;;;;;;;;;;;;;;0CAUpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;0DAEvB,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;wDAAK,WAAU;kEAA2B;;;;;;;;;;;;0DAE7C,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAMrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,8OAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;kDAEhC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,8OAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;kDAEhC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,8OAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQxC,8OAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAyN;;;;;;8CAGxO,8OAAC;oCAAG,WAAU;;wCAAgE;sDAE5E,8OAAC;4CAAK,WAAU;sDAAkF;;;;;;;;;;;;8CAIpG,8OAAC;oCAAE,WAAU;8CAA0D;;;;;;;;;;;;sCAMzE,8OAAC;4BAAI,WAAU;;8CAGb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDAEf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;sEAEnB,8OAAC;4DAAI,WAAU;sEAAoE;;;;;;;;;;;;8DAKrF,8OAAC;oDAAG,WAAU;8DAAkD;;;;;;8DAGhE,8OAAC;oDAAE,WAAU;8DAA6C;;;;;;8DAK1D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;8EACvB,8OAAC;oEAAI,WAAU;8EAAuB;;;;;;8EACtC,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;sEAEzC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;8EACvB,8OAAC;oEAAI,WAAU;8EAAuB;;;;;;8EACtC,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAI3C,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;;wDAAuQ;sEAErS,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;8CAM5B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDAEf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,8MAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;8DAGxB,8OAAC;oDAAG,WAAU;8DAAsC;;;;;;8DAGpD,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAIlD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;;;;;;;sEAE1C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;;;;;;;sEAE1C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAI5C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAmC;;;;;;sEAClD,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;8CAM7C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDAEf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;sEAExB,8OAAC;4DAAI,WAAU;sEAAqE;;;;;;;;;;;;8DAKtF,8OAAC;oDAAG,WAAU;8DAAsC;;;;;;8DAGpD,8OAAC;oDAAE,WAAU;8DAA6C;;;;;;8DAI1D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAsC;;;;;;8EACrD,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;sEAEzC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAoC;;;;;;8EACnD,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;sEAEzC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAsC;;;;;;8EACrD,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAO/C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDAEf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAGnB,8OAAC;oDAAG,WAAU;8DAAsC;;;;;;8DAGpD,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAIlD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,8OAAC;oEAAK,WAAU;8EAA4B;;;;;;;;;;;;sEAE9C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,8OAAC;oEAAK,WAAU;8EAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUvD,8OAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCAAkD;sDAE9D,8OAAC;4CAAK,WAAU;sDAAkF;;;;;;;;;;;;8CAIpG,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEAAW;;;;;;;;;;;8DAE5B,8OAAC;oDAAG,WAAU;8DAAsC;;;;;;8DACpD,8OAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAClC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;8CAMZ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEAAW;;;;;;;;;;;8DAE5B,8OAAC;oDAAG,WAAU;8DAAsC;;;;;;8DACpD,8OAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAClC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;sEACrB,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;8CAMZ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEAAW;;;;;;;;;;;8DAE5B,8OAAC;oDAAG,WAAU;8DAAsC;;;;;;8DACpD,8OAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAClC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAwN;;;;;;kDAIvO,8OAAC;wCAAG,WAAU;;4CAAgE;0DAE5E,8OAAC;gDAAK,WAAU;0DAAkF;;;;;;;;;;;;kDAKpG,8OAAC;wCAAE,WAAU;kDAA4E;;;;;;kDAKzF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;;kEAChC,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEACf,8OAAC;kEAAK;;;;;;kEACN,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,8OAAC;wDAAI,WAAU;;;;;;;;;;;;0DAGjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAA6B;;;;;;kEAC5C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACZ;uEAAI,MAAM;iEAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;wEAAY,WAAU;kFAA2J;uEAAxK;;;;;;;;;;0EAKd,8OAAC;gEAAK,WAAU;0EAAuB;;;;;;;;;;;;;;;;;;;;;;;;kDAK7C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlB,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,4MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;kDAErB,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAAiC;;;;;;0DACjD,8OAAC;gDAAI,WAAU;0DAAsC;;;;;;;;;;;;;;;;;;0CAIzD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAG5C,8OAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrD", "debugId": null}}]}