/**
 * 📌 RistoFlow Type Definitions
 * 🧠 Central type definitions for the entire application
 */

// ============================================================================
// USER & AUTHENTICATION TYPES
// ============================================================================

export interface User {
  id: string
  email: string
  name?: string
  avatar_url?: string
  created_at: string
  updated_at: string
}

export interface UserProfile extends User {
  phone?: string
  timezone?: string
  language?: string
  subscription_plan: BillingPlan
  subscription_status: 'active' | 'inactive' | 'cancelled' | 'past_due'
}

// ============================================================================
// RESTAURANT & BUSINESS TYPES
// ============================================================================

export interface Restaurant {
  id: string
  name: string
  slug: string
  description?: string
  owner_id: string
  
  // Business Info
  phone?: string
  email?: string
  website?: string
  address?: Address
  
  // Branding
  logo_url?: string
  cover_image_url?: string
  brand_colors?: BrandColors
  
  // Settings
  settings: RestaurantSettings
  site_config: SiteConfig
  
  // Status
  is_active: boolean
  is_published: boolean
  
  // Timestamps
  created_at: string
  updated_at: string
}

export interface Address {
  street: string
  city: string
  state: string
  postal_code: string
  country: string
  latitude?: number
  longitude?: number
}

export interface BrandColors {
  primary: string
  secondary: string
  accent: string
  background: string
  text: string
}

export interface RestaurantSettings {
  timezone: string
  currency: string
  language: string
  business_hours: BusinessHours[]
  online_ordering: boolean
  reservations: boolean
  delivery: boolean
  pickup: boolean
}

export interface BusinessHours {
  day: 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday'
  is_open: boolean
  open_time?: string
  close_time?: string
}

// ============================================================================
// WEBSITE BUILDER TYPES
// ============================================================================

export interface SiteConfig {
  template: string
  theme: 'light' | 'dark' | 'auto'
  layout: 'modern' | 'classic' | 'minimal'
  sections: SiteSection[]
  seo: SEOConfig
  social_links?: SocialLinks
}

export interface SiteSection {
  id: string
  type: SectionType
  title?: string
  content?: any
  settings?: any
  order: number
  is_visible: boolean
}

export type SectionType = 
  | 'hero'
  | 'about'
  | 'menu'
  | 'gallery'
  | 'reviews'
  | 'contact'
  | 'events'
  | 'team'
  | 'custom'

export interface SEOConfig {
  title: string
  description: string
  keywords?: string[]
  og_image?: string
  canonical_url?: string
}

export interface SocialLinks {
  facebook?: string
  instagram?: string
  twitter?: string
  tiktok?: string
  youtube?: string
}

// ============================================================================
// MENU & ORDERING TYPES
// ============================================================================

export interface MenuCategory {
  id: string
  restaurant_id: string
  name: string
  description?: string
  image_url?: string
  order: number
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface MenuItem {
  id: string
  restaurant_id: string
  category_id: string
  name: string
  description?: string
  price: number
  image_url?: string
  
  // Options
  variants?: MenuItemVariant[]
  modifiers?: MenuItemModifier[]
  
  // Availability
  is_available: boolean
  availability_schedule?: AvailabilitySchedule[]
  
  // Metadata
  tags?: string[]
  allergens?: string[]
  nutritional_info?: NutritionalInfo
  
  // Status
  is_active: boolean
  order: number
  
  // Timestamps
  created_at: string
  updated_at: string
}

export interface MenuItemVariant {
  id: string
  name: string
  price_adjustment: number
}

export interface MenuItemModifier {
  id: string
  name: string
  options: ModifierOption[]
  is_required: boolean
  max_selections?: number
}

export interface ModifierOption {
  id: string
  name: string
  price_adjustment: number
}

export interface AvailabilitySchedule {
  day: string
  start_time: string
  end_time: string
}

export interface NutritionalInfo {
  calories?: number
  protein?: number
  carbs?: number
  fat?: number
  fiber?: number
  sodium?: number
}

// ============================================================================
// ORDERS & RESERVATIONS TYPES
// ============================================================================

export interface Order {
  id: string
  restaurant_id: string
  customer_id?: string
  
  // Order Details
  items: OrderItem[]
  subtotal: number
  tax: number
  tip?: number
  total: number
  
  // Order Info
  type: 'dine_in' | 'takeaway' | 'delivery'
  status: OrderStatus
  payment_status: 'pending' | 'paid' | 'failed' | 'refunded'
  
  // Customer Info
  customer_name?: string
  customer_phone?: string
  customer_email?: string
  
  // Delivery Info (if applicable)
  delivery_address?: Address
  delivery_instructions?: string
  
  // Timestamps
  ordered_at: string
  estimated_ready_at?: string
  completed_at?: string
  created_at: string
  updated_at: string
}

export interface OrderItem {
  id: string
  menu_item_id: string
  quantity: number
  unit_price: number
  total_price: number
  selected_variant?: string
  selected_modifiers?: string[]
  special_instructions?: string
}

export type OrderStatus = 
  | 'pending'
  | 'confirmed'
  | 'preparing'
  | 'ready'
  | 'completed'
  | 'cancelled'

export interface Reservation {
  id: string
  restaurant_id: string
  customer_name: string
  customer_phone: string
  customer_email?: string
  party_size: number
  reservation_date: string
  reservation_time: string
  status: 'pending' | 'confirmed' | 'seated' | 'completed' | 'cancelled' | 'no_show'
  special_requests?: string
  table_number?: string
  created_at: string
  updated_at: string
}

// ============================================================================
// BILLING & SUBSCRIPTION TYPES
// ============================================================================

export type BillingPlan = 'free' | 'pro' | 'enterprise'

export interface Subscription {
  id: string
  user_id: string
  plan: BillingPlan
  status: 'active' | 'inactive' | 'cancelled' | 'past_due'
  current_period_start: string
  current_period_end: string
  stripe_subscription_id?: string
  stripe_customer_id?: string
  created_at: string
  updated_at: string
}

// ============================================================================
// ANALYTICS TYPES
// ============================================================================

export interface AnalyticsData {
  restaurant_id: string
  date: string
  
  // Revenue
  total_revenue: number
  order_count: number
  average_order_value: number
  
  // Traffic
  website_visits: number
  unique_visitors: number
  page_views: number
  
  // Popular items
  top_menu_items: TopMenuItem[]
  
  // Customer data
  new_customers: number
  returning_customers: number
}

export interface TopMenuItem {
  menu_item_id: string
  name: string
  quantity_sold: number
  revenue: number
}

// ============================================================================
// API RESPONSE TYPES
// ============================================================================

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}
