/**
 * 📌 RistoFlow Landing Page - 2025 Edition
 * 🧠 Ultra-modern landing page with cutting-edge 2025 design trends
 */

import Link from "next/link"
import { ArrowRight, CheckCircle, Globe, Smartphone, TrendingUp, Users, Zap, Star, Sparkles, ChefHat, CreditCard, BarChart3, Calendar, MessageSquare } from "lucide-react"

export default function Home() {
  return (
    <div className="min-h-screen bg-[#0a0a0a] text-white overflow-hidden relative">
      {/* Animated Background */}
      <div className="fixed inset-0 -z-10">
        <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 via-red-500/5 to-pink-500/5"></div>
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-red-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-conic from-orange-500/5 via-red-500/5 to-pink-500/5 rounded-full blur-3xl animate-spin-slow"></div>

        {/* Grid Pattern */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]"></div>
      </div>

      {/* Navigation - Neubrutalist Style */}
      <nav className="relative z-50 border-b border-white/10 bg-black/50 backdrop-blur-xl">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            <div className="flex items-center space-x-3">
              <div className="relative">
                <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center shadow-2xl shadow-orange-500/25 rotate-3 hover:rotate-0 transition-transform duration-300">
                  <ChefHat className="w-6 h-6 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full animate-ping"></div>
              </div>
              <div>
                <span className="text-2xl font-black tracking-tight bg-gradient-to-r from-white via-orange-200 to-red-200 bg-clip-text text-transparent">
                  RistoFlow
                </span>
                <div className="text-xs text-orange-400 font-medium">2025 EDITION</div>
              </div>
            </div>

            <div className="hidden lg:flex items-center space-x-8">
              <Link href="#features" className="text-white/70 hover:text-white transition-colors font-medium hover:scale-105 transform duration-200">
                Features
              </Link>
              <Link href="#showcase" className="text-white/70 hover:text-white transition-colors font-medium hover:scale-105 transform duration-200">
                Showcase
              </Link>
              <Link href="#pricing" className="text-white/70 hover:text-white transition-colors font-medium hover:scale-105 transform duration-200">
                Pricing
              </Link>
              <div className="relative group">
                <Link href="/dashboard" className="bg-gradient-to-r from-orange-500 to-red-600 text-white px-6 py-3 rounded-2xl font-bold hover:from-orange-400 hover:to-red-500 transition-all duration-300 shadow-2xl shadow-orange-500/25 hover:shadow-orange-500/40 hover:scale-105 transform flex items-center space-x-2 border-2 border-orange-400/20">
                  <Sparkles className="w-4 h-4" />
                  <span>Launch App</span>
                </Link>
                <div className="absolute -inset-1 bg-gradient-to-r from-orange-500 to-red-600 rounded-2xl blur opacity-30 group-hover:opacity-50 transition duration-300 -z-10"></div>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section - Spatial Design */}
      <section className="relative min-h-screen flex items-center justify-center px-6 lg:px-8">
        <div className="max-w-7xl mx-auto w-full">
          {/* Bento Grid Layout */}
          <div className="grid grid-cols-12 grid-rows-8 gap-4 h-[90vh] max-h-[800px]">

            {/* Main Hero Content - Large Bento Box */}
            <div className="col-span-12 lg:col-span-8 row-span-5 bg-gradient-to-br from-white/5 to-white/10 backdrop-blur-xl rounded-3xl border border-white/20 p-8 lg:p-12 flex flex-col justify-center relative overflow-hidden group hover:from-white/10 hover:to-white/15 transition-all duration-500">
              <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-red-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

              <div className="relative z-10">
                <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-orange-500/20 to-red-500/20 border border-orange-500/30 text-orange-300 text-sm font-bold mb-8 backdrop-blur-sm">
                  <Sparkles className="w-4 h-4 mr-2 animate-pulse" />
                  REVOLUTIONARY RESTAURANT TECH
                </div>

                <h1 className="text-4xl lg:text-7xl xl:text-8xl font-black leading-none mb-6 tracking-tight">
                  <span className="block text-white">Build Your</span>
                  <span className="block bg-gradient-to-r from-orange-400 via-red-400 to-pink-400 bg-clip-text text-transparent animate-gradient-x">
                    Digital Empire
                  </span>
                </h1>

                <p className="text-xl lg:text-2xl text-white/70 mb-8 max-w-2xl leading-relaxed font-medium">
                  The most advanced SaaS platform for restaurants. Create stunning websites,
                  manage operations, and scale your business with AI-powered insights.
                </p>

                <div className="flex flex-col sm:flex-row gap-4">
                  <Link href="/dashboard" className="group relative bg-gradient-to-r from-orange-500 to-red-600 text-white px-8 py-4 rounded-2xl font-bold hover:from-orange-400 hover:to-red-500 transition-all duration-300 shadow-2xl shadow-orange-500/25 hover:shadow-orange-500/40 hover:scale-105 transform flex items-center justify-center space-x-2">
                    <Zap className="w-5 h-5" />
                    <span>Start Building Free</span>
                    <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                    <div className="absolute -inset-1 bg-gradient-to-r from-orange-500 to-red-600 rounded-2xl blur opacity-30 group-hover:opacity-50 transition duration-300 -z-10"></div>
                  </Link>

                  <Link href="#showcase" className="px-8 py-4 rounded-2xl border-2 border-white/20 text-white hover:border-orange-500/50 hover:bg-white/5 transition-all duration-300 font-bold flex items-center justify-center space-x-2 backdrop-blur-sm">
                    <Globe className="w-5 h-5" />
                    <span>View Showcase</span>
                  </Link>
                </div>
              </div>

              {/* Floating Elements */}
              <div className="absolute top-4 right-4 w-16 h-16 bg-gradient-to-br from-orange-500/20 to-red-500/20 rounded-2xl backdrop-blur-sm border border-white/10 flex items-center justify-center rotate-12 hover:rotate-0 transition-transform duration-300">
                <ChefHat className="w-8 h-8 text-orange-300" />
              </div>
            </div>

            {/* Stats Card */}
            <div className="col-span-12 lg:col-span-4 row-span-2 bg-gradient-to-br from-green-500/10 to-emerald-500/10 backdrop-blur-xl rounded-3xl border border-green-500/20 p-6 flex flex-col justify-center hover:from-green-500/15 hover:to-emerald-500/15 transition-all duration-500 group">
              <div className="text-center">
                <div className="text-4xl font-black text-green-400 mb-2">10K+</div>
                <div className="text-white/70 font-medium">Restaurants Powered</div>
                <div className="mt-4 flex justify-center">
                  <div className="flex -space-x-2">
                    {[...Array(5)].map((_, i) => (
                      <div key={i} className="w-8 h-8 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full border-2 border-black flex items-center justify-center text-xs font-bold text-black">
                        {i + 1}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Feature Preview */}
            <div className="col-span-12 lg:col-span-4 row-span-3 bg-gradient-to-br from-blue-500/10 to-purple-500/10 backdrop-blur-xl rounded-3xl border border-blue-500/20 p-6 flex flex-col justify-between hover:from-blue-500/15 hover:to-purple-500/15 transition-all duration-500 group">
              <div>
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <BarChart3 className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-bold text-white mb-2">AI Analytics</h3>
                <p className="text-white/70 text-sm">Smart insights that predict trends and optimize your menu for maximum profit.</p>
              </div>
              <div className="mt-4 space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-white/60">Revenue Growth</span>
                  <span className="text-green-400 font-bold">+127%</span>
                </div>
                <div className="w-full bg-white/10 rounded-full h-2">
                  <div className="bg-gradient-to-r from-green-400 to-emerald-500 h-2 rounded-full w-4/5 animate-pulse"></div>
                </div>
              </div>
            </div>

            {/* Trust Indicators */}
            <div className="col-span-12 lg:col-span-8 row-span-1 bg-gradient-to-r from-white/5 to-white/10 backdrop-blur-xl rounded-3xl border border-white/20 p-4 flex items-center justify-center space-x-8 hover:from-white/10 hover:to-white/15 transition-all duration-500">
              <div className="flex items-center space-x-2 text-white/70">
                <CheckCircle className="w-5 h-5 text-green-400" />
                <span className="font-medium">No Credit Card Required</span>
              </div>
              <div className="flex items-center space-x-2 text-white/70">
                <CheckCircle className="w-5 h-5 text-green-400" />
                <span className="font-medium">5-Minute Setup</span>
              </div>
              <div className="flex items-center space-x-2 text-white/70">
                <CheckCircle className="w-5 h-5 text-green-400" />
                <span className="font-medium">Cancel Anytime</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section - Neubrutalist Design */}
      <section id="features" className="py-32 px-6 lg:px-8 relative">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-20">
            <div className="inline-block bg-gradient-to-r from-orange-500 to-red-600 text-white px-6 py-3 rounded-2xl font-black text-sm mb-8 transform -rotate-2 hover:rotate-0 transition-transform duration-300 shadow-2xl shadow-orange-500/25">
              POWERFUL FEATURES
            </div>
            <h2 className="text-5xl lg:text-7xl font-black text-white mb-6 leading-tight">
              Everything Your
              <span className="block bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent">
                Restaurant Needs
              </span>
            </h2>
            <p className="text-xl text-white/70 max-w-3xl mx-auto leading-relaxed">
              From stunning websites to AI-powered operations management, RistoFlow revolutionizes how restaurants operate in the digital age.
            </p>
          </div>

          {/* Asymmetric Features Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">

            {/* Website Builder - Large Feature */}
            <div className="lg:col-span-8 bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl rounded-3xl border border-white/20 p-8 lg:p-12 group hover:from-white/15 hover:to-white/10 transition-all duration-500 relative overflow-hidden">
              <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full blur-2xl group-hover:scale-150 transition-transform duration-700"></div>

              <div className="relative z-10">
                <div className="flex items-start justify-between mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center group-hover:scale-110 group-hover:rotate-12 transition-all duration-300 shadow-2xl shadow-blue-500/25">
                    <Globe className="w-8 h-8 text-white" />
                  </div>
                  <div className="bg-green-400 text-black px-3 py-1 rounded-full text-xs font-black">
                    MOST POPULAR
                  </div>
                </div>

                <h3 className="text-3xl lg:text-4xl font-black text-white mb-4">
                  Visual Website Builder
                </h3>
                <p className="text-white/70 text-lg mb-6 leading-relaxed">
                  Create stunning restaurant websites with our drag-and-drop builder.
                  No coding required, just pure creativity unleashed.
                </p>

                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="bg-white/5 rounded-2xl p-4 border border-white/10">
                    <CheckCircle className="w-6 h-6 text-green-400 mb-2" />
                    <div className="text-white font-bold">50+ Templates</div>
                    <div className="text-white/60 text-sm">Professional designs</div>
                  </div>
                  <div className="bg-white/5 rounded-2xl p-4 border border-white/10">
                    <CheckCircle className="w-6 h-6 text-green-400 mb-2" />
                    <div className="text-white font-bold">Mobile First</div>
                    <div className="text-white/60 text-sm">Responsive design</div>
                  </div>
                </div>

                <Link href="/builder" className="inline-flex items-center bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-2xl font-bold hover:from-blue-400 hover:to-purple-500 transition-all duration-300 shadow-xl shadow-blue-500/25 hover:shadow-blue-500/40 hover:scale-105 transform">
                  Try Builder
                  <ArrowRight className="ml-2 w-5 h-5" />
                </Link>
              </div>
            </div>

            {/* Digital Menu - Vertical Feature */}
            <div className="lg:col-span-4 bg-gradient-to-br from-green-500/10 to-emerald-500/10 backdrop-blur-xl rounded-3xl border border-green-500/20 p-8 group hover:from-green-500/15 hover:to-emerald-500/15 transition-all duration-500 relative overflow-hidden">
              <div className="absolute -top-10 -right-10 w-24 h-24 bg-gradient-to-br from-green-500/30 to-emerald-500/30 rounded-full blur-xl group-hover:scale-125 transition-transform duration-700"></div>

              <div className="relative z-10">
                <div className="w-14 h-14 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 group-hover:-rotate-12 transition-all duration-300 shadow-2xl shadow-green-500/25">
                  <Smartphone className="w-7 h-7 text-white" />
                </div>

                <h3 className="text-2xl font-black text-white mb-4">
                  Smart Digital Menu
                </h3>
                <p className="text-white/70 mb-6 leading-relaxed">
                  QR code ordering, real-time kitchen integration, and AI-powered menu optimization.
                </p>

                <div className="space-y-3 mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span className="text-white/80 text-sm">QR Code Ordering</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span className="text-white/80 text-sm">Kitchen Display</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span className="text-white/80 text-sm">Payment Integration</span>
                  </div>
                </div>

                <div className="bg-green-500/20 rounded-2xl p-4 border border-green-500/30">
                  <div className="text-green-400 font-bold text-lg">+47%</div>
                  <div className="text-white/70 text-sm">Average Order Increase</div>
                </div>
              </div>
            </div>

            {/* Analytics - Wide Feature */}
            <div className="lg:col-span-7 bg-gradient-to-br from-purple-500/10 to-pink-500/10 backdrop-blur-xl rounded-3xl border border-purple-500/20 p-8 group hover:from-purple-500/15 hover:to-pink-500/15 transition-all duration-500 relative overflow-hidden">
              <div className="absolute bottom-0 left-0 w-40 h-40 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-full blur-2xl group-hover:scale-125 transition-transform duration-700"></div>

              <div className="relative z-10">
                <div className="flex items-center justify-between mb-6">
                  <div className="w-14 h-14 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center group-hover:scale-110 group-hover:rotate-12 transition-all duration-300 shadow-2xl shadow-purple-500/25">
                    <TrendingUp className="w-7 h-7 text-white" />
                  </div>
                  <div className="bg-purple-400 text-black px-3 py-1 rounded-full text-xs font-black">
                    AI POWERED
                  </div>
                </div>

                <h3 className="text-3xl font-black text-white mb-4">
                  Predictive Analytics
                </h3>
                <p className="text-white/70 text-lg mb-6 leading-relaxed">
                  AI-driven insights that predict customer behavior, optimize pricing, and maximize your restaurant's profitability.
                </p>

                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-black text-purple-400">127%</div>
                    <div className="text-white/60 text-sm">Revenue Growth</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-black text-pink-400">89%</div>
                    <div className="text-white/60 text-sm">Cost Reduction</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-black text-purple-400">24/7</div>
                    <div className="text-white/60 text-sm">Monitoring</div>
                  </div>
                </div>
              </div>
            </div>

            {/* CRM - Square Feature */}
            <div className="lg:col-span-5 bg-gradient-to-br from-orange-500/10 to-red-500/10 backdrop-blur-xl rounded-3xl border border-orange-500/20 p-8 group hover:from-orange-500/15 hover:to-red-500/15 transition-all duration-500 relative overflow-hidden">
              <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-orange-500/20 to-red-500/20 rounded-full blur-xl group-hover:scale-150 transition-transform duration-700"></div>

              <div className="relative z-10">
                <div className="w-14 h-14 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 group-hover:-rotate-12 transition-all duration-300 shadow-2xl shadow-orange-500/25">
                  <Users className="w-7 h-7 text-white" />
                </div>

                <h3 className="text-2xl font-black text-white mb-4">
                  Customer CRM
                </h3>
                <p className="text-white/70 mb-6 leading-relaxed">
                  Build lasting relationships with integrated CRM, loyalty programs, and automated marketing campaigns.
                </p>

                <div className="space-y-4">
                  <div className="flex items-center justify-between bg-white/5 rounded-xl p-3 border border-white/10">
                    <span className="text-white/80 text-sm">Customer Retention</span>
                    <span className="text-orange-400 font-bold">+156%</span>
                  </div>
                  <div className="flex items-center justify-between bg-white/5 rounded-xl p-3 border border-white/10">
                    <span className="text-white/80 text-sm">Loyalty Members</span>
                    <span className="text-red-400 font-bold">12.5K</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Showcase Section */}
      <section id="showcase" className="py-32 px-6 lg:px-8 relative">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-20">
            <h2 className="text-5xl lg:text-6xl font-black text-white mb-6">
              See RistoFlow
              <span className="block bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent">
                In Action
              </span>
            </h2>
            <p className="text-xl text-white/70 max-w-2xl mx-auto">
              Real restaurants, real results. See how RistoFlow transforms businesses.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Showcase Item 1 */}
            <div className="group relative bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl rounded-3xl border border-white/20 p-8 hover:from-white/15 hover:to-white/10 transition-all duration-500 overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-emerald-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="relative z-10">
                <div className="w-full h-48 bg-gradient-to-br from-green-500/20 to-emerald-500/20 rounded-2xl mb-6 flex items-center justify-center border border-green-500/30">
                  <div className="text-6xl">🍕</div>
                </div>
                <h3 className="text-2xl font-black text-white mb-3">Mario's Pizzeria</h3>
                <p className="text-white/70 mb-4">Increased online orders by 340% in just 3 months</p>
                <div className="flex items-center space-x-2 text-green-400 font-bold">
                  <TrendingUp className="w-5 h-5" />
                  <span>+340% Orders</span>
                </div>
              </div>
            </div>

            {/* Showcase Item 2 */}
            <div className="group relative bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl rounded-3xl border border-white/20 p-8 hover:from-white/15 hover:to-white/10 transition-all duration-500 overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="relative z-10">
                <div className="w-full h-48 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-2xl mb-6 flex items-center justify-center border border-blue-500/30">
                  <div className="text-6xl">🥘</div>
                </div>
                <h3 className="text-2xl font-black text-white mb-3">Spice Garden</h3>
                <p className="text-white/70 mb-4">Streamlined operations and reduced costs by 45%</p>
                <div className="flex items-center space-x-2 text-blue-400 font-bold">
                  <BarChart3 className="w-5 h-5" />
                  <span>-45% Costs</span>
                </div>
              </div>
            </div>

            {/* Showcase Item 3 */}
            <div className="group relative bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl rounded-3xl border border-white/20 p-8 hover:from-white/15 hover:to-white/10 transition-all duration-500 overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-red-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="relative z-10">
                <div className="w-full h-48 bg-gradient-to-br from-orange-500/20 to-red-500/20 rounded-2xl mb-6 flex items-center justify-center border border-orange-500/30">
                  <div className="text-6xl">☕</div>
                </div>
                <h3 className="text-2xl font-black text-white mb-3">Brew & Beans</h3>
                <p className="text-white/70 mb-4">Built a loyal customer base of 5,000+ members</p>
                <div className="flex items-center space-x-2 text-orange-400 font-bold">
                  <Users className="w-5 h-5" />
                  <span>5K+ Members</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section - Neubrutalist */}
      <section className="py-32 px-6 lg:px-8 relative">
        <div className="max-w-5xl mx-auto text-center">
          <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl rounded-3xl border border-white/20 p-12 lg:p-16 relative overflow-hidden group">
            <div className="absolute inset-0 bg-gradient-to-br from-orange-500/10 to-red-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>

            <div className="relative z-10">
              <div className="inline-block bg-gradient-to-r from-orange-500 to-red-600 text-white px-6 py-3 rounded-2xl font-black text-sm mb-8 transform rotate-2 hover:rotate-0 transition-transform duration-300 shadow-2xl shadow-orange-500/25">
                LIMITED TIME OFFER
              </div>

              <h2 className="text-5xl lg:text-7xl font-black text-white mb-6 leading-tight">
                Ready to
                <span className="block bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent">
                  Dominate?
                </span>
              </h2>

              <p className="text-xl lg:text-2xl text-white/70 mb-10 max-w-3xl mx-auto leading-relaxed">
                Join 10,000+ restaurants already using RistoFlow to revolutionize their business.
                Start your free trial today and see results in 24 hours.
              </p>

              <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
                <Link href="/dashboard" className="group relative bg-gradient-to-r from-orange-500 to-red-600 text-white px-10 py-5 rounded-2xl font-black text-xl hover:from-orange-400 hover:to-red-500 transition-all duration-300 shadow-2xl shadow-orange-500/25 hover:shadow-orange-500/40 hover:scale-105 transform flex items-center space-x-3">
                  <Zap className="w-6 h-6" />
                  <span>Start Free Trial</span>
                  <ArrowRight className="w-6 h-6 group-hover:translate-x-1 transition-transform" />
                  <div className="absolute -inset-1 bg-gradient-to-r from-orange-500 to-red-600 rounded-2xl blur opacity-30 group-hover:opacity-50 transition duration-300 -z-10"></div>
                </Link>

                <div className="text-center">
                  <div className="text-white/60 text-sm mb-2">Trusted by</div>
                  <div className="flex items-center space-x-2">
                    <div className="flex -space-x-2">
                      {[...Array(5)].map((_, i) => (
                        <div key={i} className="w-8 h-8 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full border-2 border-black flex items-center justify-center text-xs font-bold text-black">
                          ★
                        </div>
                      ))}
                    </div>
                    <span className="text-white font-bold">10,000+ restaurants</span>
                  </div>
                </div>
              </div>

              <div className="mt-8 flex flex-wrap justify-center gap-8 text-white/60 text-sm">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span>No Credit Card Required</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span>5-Minute Setup</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span>Cancel Anytime</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span>24/7 Support</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer - Minimal */}
      <footer className="py-16 px-6 lg:px-8 border-t border-white/10">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col lg:flex-row justify-between items-center">
            <div className="flex items-center space-x-3 mb-8 lg:mb-0">
              <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center shadow-2xl shadow-orange-500/25">
                <ChefHat className="w-6 h-6 text-white" />
              </div>
              <div>
                <span className="text-2xl font-black text-white">RistoFlow</span>
                <div className="text-xs text-orange-400 font-medium">2025 EDITION</div>
              </div>
            </div>

            <div className="text-center lg:text-right">
              <div className="text-white/60 text-sm mb-2">
                © 2025 RistoFlow. Built with ❤️ for restaurants worldwide.
              </div>
              <div className="text-white/40 text-xs">
                Powered by Next.js 15 • Supabase • AI • Love
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
